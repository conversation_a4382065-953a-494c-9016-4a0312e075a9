# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# Go files
[*.go]
indent_style = tab
indent_size = 4

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 4

# Makefile
[Makefile]
indent_style = tab

# Configuration files
[*.{toml,ini,cfg}]
indent_style = space
indent_size = 2

# License and readme files
[{LICENSE,README,CHANGELOG,CONTRIBUTING}]
indent_style = space
indent_size = 2