# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Build artifacts
dist/
build/
bin/

# Compiled binaries from demos
main
cmd/*/main
cmd/wallet-demo/wallet-demo
cmd/rpc-demo/rpc-demo
cmd/websocket-demo/websocket-demo
cmd/transaction-demo/transaction-demo

# Test coverage files
coverage.out
coverage.html

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup

# Private keys and sensitive data
*.key
*.pem
*.p12
*.pfx
private/
secrets/

# Documentation build files
docs/_build/
docs/site/

# Local configuration files
config.local.json
config.local.yaml
config.local.toml

# Profiling files
*.prof
*.pprof

# Debug files
debug
*.debug

# Cache directories
.cache/
.tmp/

# Node.js files (if using any JS tooling)
node_modules/
package-lock.json
yarn.lock

# Python files (if using any Python tooling)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/