// Package config provides configuration constants for the Solana Go SDK.
// It includes network endpoints, timeout settings, and program identifiers.
package config

import "time"

const (
	// Network endpoints
	MainnetRPCEndpoint = "https://api.mainnet-beta.solana.com"
	TestnetRPCEndpoint = "https://api.testnet.solana.com"
	DevnetRPCEndpoint  = "https://api.devnet.solana.com"
	
	// WebSocket endpoints
	MainnetWSEndpoint = "wss://api.mainnet-beta.solana.com"
	TestnetWSEndpoint = "wss://api.testnet.solana.com"
	DevnetWSEndpoint  = "wss://api.devnet.solana.com"
	
	// Transaction and block limits
	MaxTransactionSize = 1232
	MaxBlockSize       = 1000000
	
	// Timeout configurations
	DefaultTimeout     = 30 * time.Second
	WebSocketTimeout   = 60 * time.Second
	
	// BIP32 derivation path
	DefaultDerivationPath = "m/44'/501'/0'/0'"
	
	// Native SOL token
	SOLTokenMint = "11111111111111111111111111111112"
	
	// Program IDs
	SystemProgramID      = "11111111111111111111111111111111"
	TokenProgramID       = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
	Token2022ProgramID   = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
	AssociatedTokenProgramID = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
)