// Package examples provides example code demonstrating how to use the Solana Go SDK.
// It includes basic wallet operations, RPC calls, and transaction building examples.
package examples

import (
	"context"
	"fmt"
	"time"

	"github.com/gagliardetto/solana-go"
	"sol-go-sdk/pkg/rpc"
	"sol-go-sdk/pkg/types"
	"sol-go-sdk/pkg/wallet"
)

// SimpleUsageExample demonstrates basic SDK usage
func SimpleUsageExample() {
	fmt.Println("Solana Go SDK - Simple Usage Example")
	fmt.Println("====================================")

	// Example 1: Create a new wallet
	fmt.Println("\n1. Creating a new wallet...")
	newWallet, err := wallet.NewWallet()
	if err != nil {
		fmt.Printf("Failed to create wallet: %v\n", err)
		return
	}
	fmt.Printf("New wallet address: %s\n", newWallet.Address())
	fmt.Printf("Mnemonic: %s\n", newWallet.Mnemonic())

	// Example 2: Create wallet from mnemonic
	fmt.Println("\n2. Creating wallet from mnemonic...")
	mnemonic := "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
	walletFromMnemonic, err := wallet.NewWalletFromMnemonic(mnemonic, "")
	if err != nil {
		fmt.Printf("Failed to create wallet from mnemonic: %v\n", err)
		return
	}
	fmt.Printf("Wallet from mnemonic address: %s\n", walletFromMnemonic.Address())

	// Example 3: Create RPC client
	fmt.Println("\n3. Creating RPC client...")
	client := rpc.NewClient("https://api.devnet.solana.com")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Example 4: Get latest blockhash
	fmt.Println("\n4. Getting latest blockhash...")
	blockhashResult, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
	if err != nil {
		fmt.Printf("Failed to get latest blockhash: %v\n", err)
	} else {
		fmt.Printf("Latest blockhash: %s\n", blockhashResult.Value.Blockhash)
	}

	// Example 5: Get account balance
	fmt.Println("\n5. Getting account balance...")
	balance, err := client.GetBalance(ctx, walletFromMnemonic.PublicKey(), types.CommitmentFinalized)
	if err != nil {
		fmt.Printf("Failed to get balance: %v\n", err)
	} else {
		fmt.Printf("Account balance: %d lamports\n", balance.Value)
	}

	// Example 6: Multiple account derivation
	fmt.Println("\n6. Deriving multiple accounts...")
	accounts, err := walletFromMnemonic.DeriveMultipleAccounts(3)
	if err != nil {
		fmt.Printf("Failed to derive multiple accounts: %v\n", err)
	} else {
		fmt.Printf("Derived %d accounts:\n", len(accounts))
		for i, account := range accounts {
			fmt.Printf("  Account %d: %s\n", i, account.Address())
		}
	}

	// Example 7: Validate addresses
	fmt.Println("\n7. Validating addresses...")
	validAddress := "********************************"
	invalidAddress := "invalid_address"
	
	if _, err := solana.PublicKeyFromBase58(validAddress); err != nil {
		fmt.Printf("Address %s is invalid: %v\n", validAddress, err)
	} else {
		fmt.Printf("Address %s is valid\n", validAddress)
	}
	
	if _, err := solana.PublicKeyFromBase58(invalidAddress); err != nil {
		fmt.Printf("Address %s is invalid: %v\n", invalidAddress, err)
	} else {
		fmt.Printf("Address %s is valid\n", invalidAddress)
	}

	fmt.Println("\n✅ Simple usage example completed!")
}