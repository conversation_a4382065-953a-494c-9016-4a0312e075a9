package utils

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	
	"github.com/mr-tron/base58"
)

// Base58Encode encodes bytes to base58
func Base58Encode(data []byte) string {
	return base58.Encode(data)
}

// Base58Decode decodes base58 string to bytes
func Base58Decode(encoded string) ([]byte, error) {
	return base58.Decode(encoded)
}

// Base64Encode encodes bytes to base64
func Base64Encode(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// Base64Decode decodes base64 string to bytes
func Base64Decode(encoded string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(encoded)
}

// HexEncode encodes bytes to hex string
func HexEncode(data []byte) string {
	return hex.EncodeToString(data)
}

// HexDecode decodes hex string to bytes
func HexDecode(encoded string) ([]byte, error) {
	return hex.DecodeString(encoded)
}

// BytesToString converts bytes to string with specified encoding
func BytesToString(data []byte, encoding string) (string, error) {
	switch encoding {
	case "base58":
		return Base58Encode(data), nil
	case "base64":
		return Base64Encode(data), nil
	case "hex":
		return HexEncode(data), nil
	default:
		return "", fmt.Errorf("unsupported encoding: %s", encoding)
	}
}

// StringToBytes converts string to bytes with specified encoding
func StringToBytes(encoded string, encoding string) ([]byte, error) {
	switch encoding {
	case "base58":
		return Base58Decode(encoded)
	case "base64":
		return Base64Decode(encoded)
	case "hex":
		return HexDecode(encoded)
	default:
		return nil, fmt.Errorf("unsupported encoding: %s", encoding)
	}
}