// Package utils provides utility functions for validation and encoding operations.
// It includes validators for Solana addresses, signatures, and other data types.
package utils

import (
	"fmt"
	"regexp"
	
	"github.com/gagliardetto/solana-go"
)

var (
	// Compiled regex patterns for validation
	urlRegex  = regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	pathRegex = regexp.MustCompile(`^m(/\d+'?)+$`)
)

// ValidatePublicKey validates if a string is a valid Solana public key
func ValidatePublicKey(pubkey string) error {
	_, err := solana.PublicKeyFromBase58(pubkey)
	if err != nil {
		return fmt.Errorf("invalid public key: %w", err)
	}
	return nil
}

// ValidateSignature validates if a string is a valid Solana signature
func ValidateSignature(signature string) error {
	if len(signature) != 88 {
		return fmt.Errorf("invalid signature length: expected 88, got %d", len(signature))
	}
	
	_, err := Base58Decode(signature)
	if err != nil {
		return fmt.Errorf("invalid signature format: %w", err)
	}
	
	return nil
}

// ValidateAmount validates if an amount is valid (greater than 0)
func ValidateAmount(amount uint64) error {
	if amount == 0 {
		return fmt.Errorf("amount must be greater than 0")
	}
	return nil
}

// ValidateBlockhash validates if a string is a valid blockhash
func ValidateBlockhash(blockhash string) error {
	if len(blockhash) != 44 {
		return fmt.Errorf("invalid blockhash length: expected 44, got %d", len(blockhash))
	}
	
	_, err := Base58Decode(blockhash)
	if err != nil {
		return fmt.Errorf("invalid blockhash format: %w", err)
	}
	
	return nil
}

// ValidateURL validates if a string is a valid URL
func ValidateURL(url string) error {
	if !urlRegex.MatchString(url) {
		return fmt.Errorf("invalid URL format: %s", url)
	}
	return nil
}

// ValidateDerivationPath validates if a string is a valid BIP32 derivation path
func ValidateDerivationPath(path string) error {
	if !pathRegex.MatchString(path) {
		return fmt.Errorf("invalid derivation path format: %s", path)
	}
	return nil
}

// ValidateSlot validates if a slot number is valid
func ValidateSlot(slot uint64) error {
	if slot == 0 {
		return fmt.Errorf("slot must be greater than 0")
	}
	return nil
}