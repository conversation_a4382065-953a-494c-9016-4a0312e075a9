package rpc

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/gagliardetto/solana-go/rpc/ws"
	"sol-go-sdk/internal/config"
)

// SimpleWebSocketClient represents a simplified WebSocket client
type SimpleWebSocketClient struct {
	client   *ws.Client
	endpoint string
	timeout  time.Duration
}

// NewSimpleWebSocketClient creates a new simple WebSocket client
func NewSimpleWebSocketClient(endpoint string) (*SimpleWebSocketClient, error) {
	client, err := ws.Connect(context.Background(), endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to websocket: %w", err)
	}
	
	return &SimpleWebSocketClient{
		client:   client,
		endpoint: endpoint,
		timeout:  config.WebSocketTimeout,
	}, nil
}

// Close closes the WebSocket connection
func (w *SimpleWebSocketClient) Close() error {
	w.client.Close()
	return nil
}

// SubscribeToSlots subscribes to slot updates
func (w *SimpleWebSocketClient) SubscribeToSlots(ctx context.Context) (<-chan *ws.SlotResult, error) {
	sub, err := w.client.SlotSubscribe()
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to slots: %w", err)
	}
	
	// Use a buffered channel to prevent blocking
	const channelBufferSize = 100
	resultChan := make(chan *ws.SlotResult, channelBufferSize)
	
	go func() {
		defer close(resultChan)
		defer func() {
			sub.Unsubscribe()
		}()
		
		for {
			select {
			case <-ctx.Done():
				return
			default:
				// Add timeout context to prevent blocking forever
				recvCtx, cancel := context.WithTimeout(ctx, w.timeout)
				got, err := sub.Recv(recvCtx)
				cancel()
				
				if err != nil {
					// Check if it's a context cancellation
					if ctx.Err() != nil {
						return
					}
					// For other errors, log and continue
					fmt.Printf("Warning: failed to receive slot update: %v\n", err)
					continue
				}
				
				select {
				case resultChan <- got:
				case <-ctx.Done():
					return
				}
			}
		}
	}()
	
	return resultChan, nil
}

// GetEndpointFromRPC converts RPC endpoint to WebSocket endpoint
func GetEndpointFromRPC(rpcEndpoint string) (string, error) {
	u, err := url.Parse(rpcEndpoint)
	if err != nil {
		return "", fmt.Errorf("invalid RPC endpoint: %w", err)
	}
	
	switch u.Scheme {
	case "http":
		u.Scheme = "ws"
	case "https":
		u.Scheme = "wss"
	default:
		return "", fmt.Errorf("unsupported scheme: %s", u.Scheme)
	}
	
	return u.String(), nil
}