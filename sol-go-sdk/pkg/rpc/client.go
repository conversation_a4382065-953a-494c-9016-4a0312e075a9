// Package rpc provides RPC and WebSocket client functionality for interacting with Solana nodes.
// It includes methods for querying blockchain data and submitting transactions.
package rpc

import (
	"net"
	"net/http"
	"time"

	"sol-go-sdk/internal/config"

	"github.com/gagliardetto/solana-go/rpc"
	"github.com/gagliardetto/solana-go/rpc/jsonrpc"
)

// Client represents an RPC client for Solana
// It embeds the solana-go rpc.Client to expose all RPC methods
// with automatic timeout management for ALL methods
//
// Key Features:
// - Automatic timeout for all RPC calls at HTTP client level
// - Direct access to all solana-go RPC methods without wrapper overhead
// - Configurable HTTP transport settings
//
// Usage examples:
//
//	client := rpc.NewClient("https://api.devnet.solana.com")
//
//	// Custom timeout configuration:
//	client := rpc.NewClientWithOptions(rpc.ClientOptions{
//	    Endpoint: "https://api.mainnet-beta.solana.com",
//	    Timeout:  10 * time.Second,
//	})
//
//	// Use any solana-go RPC method directly with automatic timeout:
//	balance, err := client.GetBalance(ctx, pubkey, rpc.CommitmentFinalized)
//	supply, err := client.GetSupply(ctx, rpc.CommitmentFinalized)
//	voteAccounts, err := client.GetVoteAccounts(ctx, nil)
//	blockTime, err := client.GetBlockTime(ctx, slot)
type Client struct {
	*rpc.Client // Embedded to expose all solana-go RPC methods
	endpoint    string
	timeout     time.Duration
}

// ClientOptions represents options for creating a new client
type ClientOptions struct {
	Endpoint            string
	Timeout             time.Duration
	MaxIdleConnsPerHost int
	KeepAlive           time.Duration
	Headers             map[string]string
}

// NewClient creates a new RPC client with automatic timeout for all RPC methods
func NewClient(endpoint string) *Client {
	return NewClientWithOptions(ClientOptions{
		Endpoint: endpoint,
		Timeout:  config.DefaultTimeout,
	})
}

// NewClientWithOptions creates a new RPC client with custom options
// All RPC methods will automatically use the specified timeout
func NewClientWithOptions(opts ClientOptions) *Client {
	timeout := opts.Timeout
	if timeout == 0 {
		timeout = config.DefaultTimeout
	}

	maxIdleConns := opts.MaxIdleConnsPerHost
	if maxIdleConns == 0 {
		maxIdleConns = 10
	}

	keepAlive := opts.KeepAlive
	if keepAlive == 0 {
		keepAlive = 30 * time.Second
	}

	// Create custom HTTP transport with timeout settings
	transport := &http.Transport{
		IdleConnTimeout:     timeout,
		MaxIdleConnsPerHost: maxIdleConns,
		Proxy:               http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   timeout,
			KeepAlive: keepAlive,
		}).DialContext,
	}

	// Create custom HTTP client with timeout
	httpClient := &http.Client{
		Timeout:   timeout,
		Transport: transport,
	}

	// Create JSON-RPC client with custom HTTP client
	jsonRPCClient := jsonrpc.NewClientWithOpts(opts.Endpoint, &jsonrpc.RPCClientOpts{
		HTTPClient:    httpClient,
		CustomHeaders: opts.Headers,
	})

	return &Client{
		Client:   rpc.NewWithCustomRPCClient(jsonRPCClient),
		endpoint: opts.Endpoint,
		timeout:  timeout,
	}
}

// GetEndpoint returns the RPC endpoint
func (c *Client) GetEndpoint() string {
	return c.endpoint
}

// GetTimeout returns the configured timeout
func (c *Client) GetTimeout() time.Duration {
	return c.timeout
}
