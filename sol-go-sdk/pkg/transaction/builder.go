// Package transaction provides utilities for building and managing Solana transactions.
// It includes a builder pattern for constructing transactions with multiple instructions.
package transaction

import (
	"fmt"

	"github.com/gagliardetto/solana-go"
	"sol-go-sdk/pkg/types"
)

// Builder represents a transaction builder
type Builder struct {
	instructions      []solana.Instruction
	payer             *solana.PublicKey
	recentBlockhash   *solana.Hash
	feePayer          *solana.PublicKey
}

// NewBuilder creates a new transaction builder
func NewBuilder() *Builder {
	return &Builder{
		instructions: make([]solana.Instruction, 0),
	}
}

// AddInstruction adds an instruction to the transaction
func (b *Builder) AddInstruction(instruction solana.Instruction) *Builder {
	b.instructions = append(b.instructions, instruction)
	return b
}

// AddInstructions adds multiple instructions to the transaction
func (b *Builder) AddInstructions(instructions ...solana.Instruction) *Builder {
	b.instructions = append(b.instructions, instructions...)
	return b
}

// SetPayer sets the transaction payer
func (b *Builder) SetPayer(payer solana.PublicKey) *Builder {
	b.payer = &payer
	return b
}

// SetFeePayer sets the transaction fee payer
func (b *Builder) SetFeePayer(feePayer solana.PublicKey) *Builder {
	b.feePayer = &feePayer
	return b
}

// SetRecentBlockhash sets the recent blockhash
func (b *Builder) SetRecentBlockhash(blockhash solana.Hash) *Builder {
	b.recentBlockhash = &blockhash
	return b
}

// SetRecentBlockhashFromString sets the recent blockhash from a string
func (b *Builder) SetRecentBlockhashFromString(blockhash string) *Builder {
	hash, err := solana.HashFromBase58(blockhash)
	if err != nil {
		// Don't fail here, let Build() handle validation
		return b
	}
	b.recentBlockhash = &hash
	return b
}

// Build builds the transaction
func (b *Builder) Build() (*solana.Transaction, error) {
	if len(b.instructions) == 0 {
		return nil, fmt.Errorf("transaction must have at least one instruction")
	}

	if b.payer == nil {
		return nil, fmt.Errorf("transaction must have a payer")
	}

	if b.recentBlockhash == nil {
		return nil, fmt.Errorf("transaction must have a recent blockhash")
	}

	// Create the transaction
	tx, err := solana.NewTransaction(
		b.instructions,
		*b.recentBlockhash,
		solana.TransactionPayer(*b.payer),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	return tx, nil
}

// BuildAndSign builds and signs the transaction
func (b *Builder) BuildAndSign(signers ...solana.PrivateKey) (*solana.Transaction, error) {
	tx, err := b.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to build transaction: %w", err)
	}

	// Sign the transaction
	_, err = tx.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		for _, signer := range signers {
			if signer.PublicKey().Equals(key) {
				return &signer
			}
		}
		return nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	return tx, nil
}

// GetInstructions returns the current instructions
func (b *Builder) GetInstructions() []solana.Instruction {
	return b.instructions
}

// GetPayer returns the current payer
func (b *Builder) GetPayer() *solana.PublicKey {
	return b.payer
}

// GetRecentBlockhash returns the current recent blockhash
func (b *Builder) GetRecentBlockhash() *solana.Hash {
	return b.recentBlockhash
}

// GetFeePayer returns the current fee payer
func (b *Builder) GetFeePayer() *solana.PublicKey {
	return b.feePayer
}

// Clear clears all instructions and settings
func (b *Builder) Clear() *Builder {
	b.instructions = make([]solana.Instruction, 0)
	b.payer = nil
	b.recentBlockhash = nil
	b.feePayer = nil
	return b
}

// Clone creates a copy of the builder
func (b *Builder) Clone() *Builder {
	clone := &Builder{
		instructions: make([]solana.Instruction, len(b.instructions)),
	}
	copy(clone.instructions, b.instructions)
	
	if b.payer != nil {
		payer := *b.payer
		clone.payer = &payer
	}
	
	if b.recentBlockhash != nil {
		blockhash := *b.recentBlockhash
		clone.recentBlockhash = &blockhash
	}
	
	if b.feePayer != nil {
		feePayer := *b.feePayer
		clone.feePayer = &feePayer
	}
	
	return clone
}

// ValidateTransaction validates the transaction before building
func (b *Builder) ValidateTransaction() error {
	if len(b.instructions) == 0 {
		return fmt.Errorf("transaction must have at least one instruction")
	}

	if b.payer == nil {
		return fmt.Errorf("transaction must have a payer")
	}

	if b.recentBlockhash == nil {
		return fmt.Errorf("transaction must have a recent blockhash")
	}

	return nil
}

// EstimateTransactionSize estimates the size of the transaction
func (b *Builder) EstimateTransactionSize() (int, error) {
	tx, err := b.Build()
	if err != nil {
		return 0, fmt.Errorf("failed to build transaction for size estimation: %w", err)
	}

	serialized, err := tx.MarshalBinary()
	if err != nil {
		return 0, fmt.Errorf("failed to serialize transaction: %w", err)
	}

	return len(serialized), nil
}

// ToTransactionParams converts the builder to transaction parameters
func (b *Builder) ToTransactionParams() (*types.TransactionParams, error) {
	if err := b.ValidateTransaction(); err != nil {
		return nil, fmt.Errorf("invalid transaction: %w", err)
	}

	var feePayer *solana.PublicKey
	if b.feePayer != nil {
		feePayer = b.feePayer
	} else {
		feePayer = b.payer
	}

	return &types.TransactionParams{
		Instructions:    b.instructions,
		Payer:          *b.payer,
		RecentBlockhash: *b.recentBlockhash,
		FeePayer:       *feePayer,
	}, nil
}

