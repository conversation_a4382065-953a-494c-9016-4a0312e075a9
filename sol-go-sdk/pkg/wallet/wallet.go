// Package wallet provides functionality for creating and managing Solana wallets.
// It includes support for mnemonic phrases, key derivation, and transaction signing.
package wallet

import (
	"fmt"

	"github.com/gagliardetto/solana-go"
	"sol-go-sdk/internal/config"
)

// Wallet represents a Solana wallet
type Wallet struct {
	keyPair          *KeyPair
	mnemonic         string
	passphrase       string
	derivationPath   string
	accountIndex     int
}

// WalletOptions represents options for creating a wallet
type WalletOptions struct {
	Mnemonic       string
	Passphrase     string
	DerivationPath string
	AccountIndex   int
}

// NewWallet creates a new wallet with a random mnemonic
func NewWallet() (*Wallet, error) {
	return NewWalletWithOptions(WalletOptions{})
}

// NewWalletWithOptions creates a new wallet with the specified options
func NewWalletWithOptions(opts WalletOptions) (*Wallet, error) {
	mnemonic := opts.Mnemonic
	if mnemonic == "" {
		var err error
		mnemonic, err = GenerateRandomMnemonic()
		if err != nil {
			return nil, fmt.Errorf("failed to generate mnemonic: %w", err)
		}
	}

	passphrase := opts.Passphrase
	derivationPath := opts.DerivationPath
	if derivationPath == "" {
		if opts.AccountIndex > 0 {
			derivationPath = fmt.Sprintf("m/44'/501'/%d'/0'", opts.AccountIndex)
		} else {
			derivationPath = config.DefaultDerivationPath
		}
	}

	keyPair, err := DeriveKeyFromMnemonicWithPath(mnemonic, passphrase, derivationPath)
	if err != nil {
		return nil, fmt.Errorf("failed to derive key: %w", err)
	}

	return &Wallet{
		keyPair:          keyPair,
		mnemonic:         mnemonic,
		passphrase:       passphrase,
		derivationPath:   derivationPath,
		accountIndex:     opts.AccountIndex,
	}, nil
}

// NewWalletFromMnemonic creates a wallet from an existing mnemonic
func NewWalletFromMnemonic(mnemonic, passphrase string) (*Wallet, error) {
	return NewWalletWithOptions(WalletOptions{
		Mnemonic:   mnemonic,
		Passphrase: passphrase,
	})
}

// NewWalletFromMnemonicWithPath creates a wallet from an existing mnemonic and derivation path
func NewWalletFromMnemonicWithPath(mnemonic, passphrase, derivationPath string) (*Wallet, error) {
	return NewWalletWithOptions(WalletOptions{
		Mnemonic:       mnemonic,
		Passphrase:     passphrase,
		DerivationPath: derivationPath,
	})
}

// NewWalletFromPrivateKey creates a wallet from a private key
func NewWalletFromPrivateKey(privateKey solana.PrivateKey) (*Wallet, error) {
	keyPair, err := NewKeyPairFromPrivateKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create keypair: %w", err)
	}

	return &Wallet{
		keyPair: keyPair,
	}, nil
}

// NewWalletFromSeed creates a wallet from a seed
func NewWalletFromSeed(seed []byte) (*Wallet, error) {
	keyPair, err := NewKeyPairFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("failed to create keypair: %w", err)
	}

	return &Wallet{
		keyPair: keyPair,
	}, nil
}

// PublicKey returns the wallet's public key
func (w *Wallet) PublicKey() solana.PublicKey {
	return w.keyPair.PublicKey()
}

// PrivateKey returns the wallet's private key
func (w *Wallet) PrivateKey() solana.PrivateKey {
	return w.keyPair.PrivateKey()
}

// KeyPair returns the wallet's key pair
func (w *Wallet) KeyPair() *KeyPair {
	return w.keyPair
}

// Mnemonic returns the wallet's mnemonic (may be empty if wallet was created from private key)
func (w *Wallet) Mnemonic() string {
	return w.mnemonic
}

// Passphrase returns the wallet's passphrase
func (w *Wallet) Passphrase() string {
	return w.passphrase
}

// DerivationPath returns the wallet's derivation path
func (w *Wallet) DerivationPath() string {
	return w.derivationPath
}

// AccountIndex returns the wallet's account index
func (w *Wallet) AccountIndex() int {
	return w.accountIndex
}

// Address returns the wallet's address as a string
func (w *Wallet) Address() string {
	return w.keyPair.PublicKey().String()
}

// Sign signs a message with the wallet's private key
func (w *Wallet) Sign(message []byte) ([]byte, error) {
	return w.keyPair.Sign(message)
}

// SignTransaction signs a transaction with the wallet's private key
func (w *Wallet) SignTransaction(transaction *solana.Transaction) (*solana.Transaction, error) {
	if transaction == nil {
		return nil, fmt.Errorf("transaction cannot be nil")
	}

	// Sign the transaction
	_, err := transaction.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		if key.Equals(w.keyPair.PublicKey()) {
			priv := w.keyPair.PrivateKey()
			return &priv
		}
		return nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	return transaction, nil
}

// Verify verifies a signature against a message
func (w *Wallet) Verify(message, signature []byte) bool {
	return w.keyPair.Verify(message, signature)
}

// String returns the string representation of the wallet (public key)
func (w *Wallet) String() string {
	return w.Address()
}

// HasMnemonic returns true if the wallet has a mnemonic
func (w *Wallet) HasMnemonic() bool {
	return w.mnemonic != ""
}

// DeriveAccount derives a new account from the same mnemonic
func (w *Wallet) DeriveAccount(accountIndex int) (*Wallet, error) {
	if !w.HasMnemonic() {
		return nil, fmt.Errorf("wallet does not have a mnemonic")
	}

	return NewWalletWithOptions(WalletOptions{
		Mnemonic:     w.mnemonic,
		Passphrase:   w.passphrase,
		AccountIndex: accountIndex,
	})
}

// DeriveMultipleAccounts derives multiple accounts from the same mnemonic
func (w *Wallet) DeriveMultipleAccounts(count int) ([]*Wallet, error) {
	if !w.HasMnemonic() {
		return nil, fmt.Errorf("wallet does not have a mnemonic")
	}

	if count <= 0 {
		return nil, fmt.Errorf("count must be greater than 0")
	}

	wallets := make([]*Wallet, count)
	for i := 0; i < count; i++ {
		wallet, err := w.DeriveAccount(i)
		if err != nil {
			return nil, fmt.Errorf("failed to derive account %d: %w", i, err)
		}
		wallets[i] = wallet
	}

	return wallets, nil
}

// Clone creates a copy of the wallet
func (w *Wallet) Clone() *Wallet {
	return &Wallet{
		keyPair:          w.keyPair,
		mnemonic:         w.mnemonic,
		passphrase:       w.passphrase,
		derivationPath:   w.derivationPath,
		accountIndex:     w.accountIndex,
	}
}

// Equals checks if two wallets are equal (same public key)
func (w *Wallet) Equals(other *Wallet) bool {
	if other == nil {
		return false
	}
	return w.keyPair.PublicKey().Equals(other.keyPair.PublicKey())
}