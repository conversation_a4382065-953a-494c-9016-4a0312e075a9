package wallet

import (
	"crypto/hmac"
	"crypto/sha512"
	"encoding/binary"
	"fmt"
	"strconv"
	"strings"

	"github.com/gagliardetto/solana-go"
	"sol-go-sdk/internal/config"
	"sol-go-sdk/pkg/utils"
)

// BIP32 constants
const (
	// HardenedKeyOffset is the offset for hardened keys
	HardenedKeyOffset = 0x80000000
	// MasterKeyName is the name for the master key
	MasterKeyName = "ed25519 seed"
)

// ExtendedKey represents a BIP32 extended key
type ExtendedKey struct {
	key       []byte
	chainCode []byte
	depth     uint8
	childNum  uint32
	isPrivate bool
}

// DerivationPath represents a BIP32 derivation path
type DerivationPath struct {
	path []uint32
}

// NewDerivationPath creates a new derivation path from a string
func NewDerivationPath(path string) (*DerivationPath, error) {
	if err := utils.ValidateDerivationPath(path); err != nil {
		return nil, fmt.Errorf("invalid derivation path: %w", err)
	}

	if !strings.HasPrefix(path, "m/") {
		return nil, fmt.Errorf("derivation path must start with 'm/'")
	}

	parts := strings.Split(path[2:], "/")
	derivationPath := make([]uint32, len(parts))

	for i, part := range parts {
		var index uint32

		if strings.HasSuffix(part, "'") {
			// Hardened key
			indexStr := part[:len(part)-1]
			indexUint, parseErr := strconv.ParseUint(indexStr, 10, 32)
			if parseErr != nil {
				return nil, fmt.Errorf("invalid index in derivation path: %s", part)
			}
			index = uint32(indexUint) + HardenedKeyOffset
		} else {
			// Non-hardened key
			indexUint, parseErr := strconv.ParseUint(part, 10, 32)
			if parseErr != nil {
				return nil, fmt.Errorf("invalid index in derivation path: %s", part)
			}
			index = uint32(indexUint)
		}

		derivationPath[i] = index
	}

	return &DerivationPath{path: derivationPath}, nil
}

// NewMasterKeyFromSeed creates a master key from a seed
func NewMasterKeyFromSeed(seed []byte) (*ExtendedKey, error) {
	if len(seed) < 16 || len(seed) > 64 {
		return nil, fmt.Errorf("invalid seed length: must be between 16 and 64 bytes")
	}

	mac := hmac.New(sha512.New, []byte(MasterKeyName))
	mac.Write(seed)
	i := mac.Sum(nil)

	key := i[:32]
	chainCode := i[32:]

	return &ExtendedKey{
		key:       key,
		chainCode: chainCode,
		depth:     0,
		childNum:  0,
		isPrivate: true,
	}, nil
}

// DeriveChild derives a child key from the extended key
func (ek *ExtendedKey) DeriveChild(index uint32) (*ExtendedKey, error) {
	if !ek.isPrivate {
		return nil, fmt.Errorf("cannot derive child from public key")
	}

	if ek.depth == 255 {
		return nil, fmt.Errorf("derivation depth exceeded")
	}

	var data []byte
	if index >= HardenedKeyOffset {
		// Hardened derivation
		data = append([]byte{0x00}, ek.key...)
	} else {
		return nil, fmt.Errorf("non-hardened derivation not supported for Ed25519")
	}

	indexBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(indexBytes, index)
	data = append(data, indexBytes...)

	mac := hmac.New(sha512.New, ek.chainCode)
	mac.Write(data)
	i := mac.Sum(nil)

	childKey := i[:32]
	childChainCode := i[32:]

	return &ExtendedKey{
		key:       childKey,
		chainCode: childChainCode,
		depth:     ek.depth + 1,
		childNum:  index,
		isPrivate: true,
	}, nil
}

// DerivePath derives a key using a derivation path
func (ek *ExtendedKey) DerivePath(path *DerivationPath) (*ExtendedKey, error) {
	key := ek
	var err error

	for _, index := range path.path {
		key, err = key.DeriveChild(index)
		if err != nil {
			return nil, fmt.Errorf("failed to derive child at index %d: %w", index, err)
		}
	}

	return key, nil
}

// ToKeyPair converts the extended key to a KeyPair
func (ek *ExtendedKey) ToKeyPair() (*KeyPair, error) {
	if !ek.isPrivate {
		return nil, fmt.Errorf("cannot create keypair from public key")
	}

	return NewKeyPairFromSeed(ek.key)
}

// PrivateKey returns the private key bytes
func (ek *ExtendedKey) PrivateKey() []byte {
	if !ek.isPrivate {
		return nil
	}
	return ek.key
}

// PublicKey returns the public key
func (ek *ExtendedKey) PublicKey() (solana.PublicKey, error) {
	kp, err := ek.ToKeyPair()
	if err != nil {
		return solana.PublicKey{}, fmt.Errorf("failed to convert to keypair: %w", err)
	}
	return kp.PublicKey(), nil
}

// ChainCode returns the chain code
func (ek *ExtendedKey) ChainCode() []byte {
	return ek.chainCode
}

// Depth returns the depth of the key
func (ek *ExtendedKey) Depth() uint8 {
	return ek.depth
}

// ChildNum returns the child number
func (ek *ExtendedKey) ChildNum() uint32 {
	return ek.childNum
}

// IsPrivate returns true if this is a private key
func (ek *ExtendedKey) IsPrivate() bool {
	return ek.isPrivate
}

// String returns the string representation of the derivation path
func (dp *DerivationPath) String() string {
	if len(dp.path) == 0 {
		return "m"
	}

	parts := make([]string, len(dp.path)+1)
	parts[0] = "m"

	for i, index := range dp.path {
		if index >= HardenedKeyOffset {
			parts[i+1] = fmt.Sprintf("%d'", index-HardenedKeyOffset)
		} else {
			parts[i+1] = fmt.Sprintf("%d", index)
		}
	}

	return strings.Join(parts, "/")
}

// GetPath returns the path as a slice of indices
func (dp *DerivationPath) GetPath() []uint32 {
	return dp.path
}

// DeriveKeyFromMnemonic derives a key from mnemonic using the default Solana derivation path
func DeriveKeyFromMnemonic(mnemonic, passphrase string) (*KeyPair, error) {
	return DeriveKeyFromMnemonicWithPath(mnemonic, passphrase, config.DefaultDerivationPath)
}

// DeriveKeyFromMnemonicWithPath derives a key from mnemonic using a custom derivation path
func DeriveKeyFromMnemonicWithPath(mnemonic, passphrase, derivationPath string) (*KeyPair, error) {
	seed, err := MnemonicToSeed(mnemonic, passphrase)
	if err != nil {
		return nil, fmt.Errorf("failed to convert mnemonic to seed: %w", err)
	}

	masterKey, err := NewMasterKeyFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("failed to create master key: %w", err)
	}

	path, err := NewDerivationPath(derivationPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse derivation path: %w", err)
	}

	derivedKey, err := masterKey.DerivePath(path)
	if err != nil {
		return nil, fmt.Errorf("failed to derive key: %w", err)
	}

	return derivedKey.ToKeyPair()
}

// DeriveMultipleKeys derives multiple keys from a mnemonic using different account indices
func DeriveMultipleKeys(mnemonic, passphrase string, accountCount int) ([]*KeyPair, error) {
	if accountCount <= 0 {
		return nil, fmt.Errorf("account count must be greater than 0")
	}

	seed, err := MnemonicToSeed(mnemonic, passphrase)
	if err != nil {
		return nil, fmt.Errorf("failed to convert mnemonic to seed: %w", err)
	}

	masterKey, err := NewMasterKeyFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("failed to create master key: %w", err)
	}

	keys := make([]*KeyPair, accountCount)
	for i := 0; i < accountCount; i++ {
		derivationPath := fmt.Sprintf("m/44'/501'/%d'/0'", i)
		path, err := NewDerivationPath(derivationPath)
		if err != nil {
			return nil, fmt.Errorf("failed to parse derivation path: %w", err)
		}

		derivedKey, err := masterKey.DerivePath(path)
		if err != nil {
			return nil, fmt.Errorf("failed to derive key for account %d: %w", i, err)
		}

		keyPair, err := derivedKey.ToKeyPair()
		if err != nil {
			return nil, fmt.Errorf("failed to create keypair for account %d: %w", i, err)
		}

		keys[i] = keyPair
	}

	return keys, nil
}