package wallet

import (
	"crypto/rand"
	"fmt"
	"strings"

	"github.com/cosmos/go-bip39"
)

// MnemonicStrength represents the strength of the mnemonic
type MnemonicStrength int

const (
	// Strength128 represents 128 bits of entropy (12 words)
	Strength128 MnemonicStrength = 128
	// Strength160 represents 160 bits of entropy (15 words)
	Strength160 MnemonicStrength = 160
	// Strength192 represents 192 bits of entropy (18 words)
	Strength192 MnemonicStrength = 192
	// Strength224 represents 224 bits of entropy (21 words)
	Strength224 MnemonicStrength = 224
	// Strength256 represents 256 bits of entropy (24 words)
	Strength256 MnemonicStrength = 256
)

// GenerateMnemonic generates a new mnemonic phrase
func GenerateMnemonic(strength MnemonicStrength) (string, error) {
	entropy, err := bip39.NewEntropy(int(strength))
	if err != nil {
		return "", fmt.Errorf("failed to generate entropy: %w", err)
	}

	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to generate mnemonic: %w", err)
	}

	return mnemonic, nil
}

// GenerateRandomMnemonic generates a new 12-word mnemonic phrase
func GenerateRandomMnemonic() (string, error) {
	return GenerateMnemonic(Strength128)
}

// ValidateMnemonic validates a mnemonic phrase
func ValidateMnemonic(mnemonic string) error {
	if !bip39.IsMnemonicValid(mnemonic) {
		return fmt.Errorf("invalid mnemonic phrase")
	}
	return nil
}

// MnemonicToSeed converts a mnemonic to a seed
func MnemonicToSeed(mnemonic, passphrase string) ([]byte, error) {
	if err := ValidateMnemonic(mnemonic); err != nil {
		return nil, fmt.Errorf("invalid mnemonic: %w", err)
	}

	seed := bip39.NewSeed(mnemonic, passphrase)
	return seed, nil
}

// MnemonicToEntropy converts a mnemonic to entropy
func MnemonicToEntropy(mnemonic string) ([]byte, error) {
	if err := ValidateMnemonic(mnemonic); err != nil {
		return nil, fmt.Errorf("invalid mnemonic: %w", err)
	}

	// Note: cosmos/go-bip39 doesn't have EntropyFromMnemonic method
	// This is a placeholder implementation
	seed := bip39.NewSeed(mnemonic, "")
	return seed[:32], nil // Return first 32 bytes as entropy approximation
}

// NormalizeMnemonic normalizes a mnemonic phrase by trimming spaces and converting to lowercase
func NormalizeMnemonic(mnemonic string) string {
	words := strings.Fields(strings.TrimSpace(mnemonic))
	for i, word := range words {
		words[i] = strings.ToLower(word)
	}
	return strings.Join(words, " ")
}

// GetMnemonicWords splits a mnemonic into individual words
func GetMnemonicWords(mnemonic string) []string {
	return strings.Fields(NormalizeMnemonic(mnemonic))
}

// GetMnemonicWordCount returns the number of words in a mnemonic
func GetMnemonicWordCount(mnemonic string) int {
	return len(GetMnemonicWords(mnemonic))
}

// IsMnemonicWordCountValid checks if the word count is valid for a mnemonic
func IsMnemonicWordCountValid(wordCount int) bool {
	validCounts := []int{12, 15, 18, 21, 24}
	for _, count := range validCounts {
		if wordCount == count {
			return true
		}
	}
	return false
}

// GetMnemonicStrength returns the strength of a mnemonic based on word count
func GetMnemonicStrength(mnemonic string) (MnemonicStrength, error) {
	wordCount := GetMnemonicWordCount(mnemonic)
	
	switch wordCount {
	case 12:
		return Strength128, nil
	case 15:
		return Strength160, nil
	case 18:
		return Strength192, nil
	case 21:
		return Strength224, nil
	case 24:
		return Strength256, nil
	default:
		return 0, fmt.Errorf("invalid word count: %d", wordCount)
	}
}

// GenerateEntropyFromRandom generates entropy from random bytes
func GenerateEntropyFromRandom(bits int) ([]byte, error) {
	if bits%8 != 0 {
		return nil, fmt.Errorf("entropy bits must be divisible by 8")
	}
	
	bytes := bits / 8
	entropy := make([]byte, bytes)
	
	_, err := rand.Read(entropy)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random entropy: %w", err)
	}
	
	return entropy, nil
}