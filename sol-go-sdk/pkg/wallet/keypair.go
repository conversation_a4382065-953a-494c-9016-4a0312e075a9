package wallet

import (
	"crypto/ed25519"
	"crypto/rand"
	"fmt"

	"github.com/gagliardetto/solana-go"
)

// KeyPair represents a Solana key pair
type KeyPair struct {
	publicKey  solana.PublicKey
	privateKey solana.PrivateKey
}

// NewKeyPair creates a new random key pair
func NewKeyPair() (*KeyPair, error) {
	pub, priv, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		return nil, fmt.Errorf("failed to generate key pair: %w", err)
	}

	return &KeyPair{
		publicKey:  solana.PublicKeyFromBytes(pub),
		privateKey: solana.PrivateKey(priv),
	}, nil
}

// NewKeyPairFromPrivateKey creates a key pair from a private key
func NewKeyPairFromPrivateKey(privateKey solana.PrivateKey) (*KeyPair, error) {
	if len(privateKey) != ed25519.PrivateKeySize {
		return nil, fmt.Errorf("invalid private key size: expected %d, got %d", ed25519.PrivateKeySize, len(privateKey))
	}

	publicKey := privateKey.PublicKey()
	return &KeyPair{
		publicKey:  publicKey,
		privateKey: privateKey,
	}, nil
}

// NewKeyPairFromSeed creates a key pair from a 32-byte seed
func NewKeyPairFromSeed(seed []byte) (*KeyPair, error) {
	if len(seed) != ed25519.SeedSize {
		return nil, fmt.Errorf("invalid seed size: expected %d, got %d", ed25519.SeedSize, len(seed))
	}

	privateKey := ed25519.NewKeyFromSeed(seed)
	publicKey := privateKey.Public().(ed25519.PublicKey)

	return &KeyPair{
		publicKey:  solana.PublicKeyFromBytes(publicKey),
		privateKey: solana.PrivateKey(privateKey),
	}, nil
}

// PublicKey returns the public key
func (k *KeyPair) PublicKey() solana.PublicKey {
	return k.publicKey
}

// PrivateKey returns the private key
func (k *KeyPair) PrivateKey() solana.PrivateKey {
	return k.privateKey
}

// Sign signs a message with the private key
func (k *KeyPair) Sign(message []byte) ([]byte, error) {
	signature := ed25519.Sign(ed25519.PrivateKey(k.privateKey), message)
	return signature, nil
}

// Verify verifies a signature against a message
func (k *KeyPair) Verify(message, signature []byte) bool {
	return ed25519.Verify(ed25519.PublicKey(k.publicKey[:]), message, signature)
}

// String returns the base58 representation of the public key
func (k *KeyPair) String() string {
	return k.publicKey.String()
}

// ToBase58 returns the base58 representation of the public key
func (k *KeyPair) ToBase58() string {
	return k.publicKey.String()
}

// PrivateKeyBytes returns the private key as bytes
func (k *KeyPair) PrivateKeyBytes() []byte {
	return k.privateKey
}

// PublicKeyBytes returns the public key as bytes
func (k *KeyPair) PublicKeyBytes() []byte {
	return k.publicKey[:]
}

// IsOnCurve checks if the public key is on the Ed25519 curve
func (k *KeyPair) IsOnCurve() bool {
	return k.publicKey.IsOnCurve()
}