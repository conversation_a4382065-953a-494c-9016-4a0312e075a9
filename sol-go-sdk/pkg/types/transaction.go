// Package types provides type definitions for Solana transactions, accounts, and blocks.
// It includes structures for transaction building, parameters, and status tracking.
package types

import (
	"github.com/gagliardetto/solana-go"
)

// TransactionStatus represents the status of a transaction
type TransactionStatus string

const (
	TransactionStatusConfirmed TransactionStatus = "confirmed"
	TransactionStatusFinalized TransactionStatus = "finalized"
	TransactionStatusProcessed TransactionStatus = "processed"
)

// TransactionResult represents the result of a transaction
type TransactionResult struct {
	Signature string             `json:"signature"`
	Slot      uint64             `json:"slot"`
	BlockTime *int64             `json:"block_time"`
	Meta      *TransactionMeta   `json:"meta"`
	Transaction *solana.Transaction `json:"transaction"`
	Version   *uint8             `json:"version"`
}

// SendTransactionConfig represents configuration for sending transactions
type SendTransactionConfig struct {
	SkipPreflight       bool            `json:"skip_preflight"`
	PreflightCommitment BlockCommitment `json:"preflight_commitment"`
	Encoding            string          `json:"encoding"`
	MaxRetries          *uint           `json:"max_retries"`
	MinContextSlot      *uint64         `json:"min_context_slot"`
}

// TransactionBuilder interface for building transactions
type TransactionBuilder interface {
	AddInstruction(instruction solana.Instruction) TransactionBuilder
	SetPayer(payer solana.PublicKey) TransactionBuilder
	SetRecentBlockhash(blockhash string) TransactionBuilder
	Build() (*solana.Transaction, error)
}

// TransferParams represents parameters for a transfer transaction
type TransferParams struct {
	From   solana.PublicKey `json:"from"`
	To     solana.PublicKey `json:"to"`
	Amount uint64           `json:"amount"`
}

// TokenTransferParams represents parameters for a token transfer transaction
type TokenTransferParams struct {
	From     solana.PublicKey `json:"from"`
	To       solana.PublicKey `json:"to"`
	Amount   uint64           `json:"amount"`
	Mint     solana.PublicKey `json:"mint"`
	Owner    solana.PublicKey `json:"owner"`
	Decimals uint8            `json:"decimals"`
}

// CreateAccountParams represents parameters for creating an account
type CreateAccountParams struct {
	From     solana.PublicKey `json:"from"`
	NewAccount solana.PublicKey `json:"new_account"`
	Lamports uint64           `json:"lamports"`
	Space    uint64           `json:"space"`
	Owner    solana.PublicKey `json:"owner"`
}

// MintParams represents parameters for minting tokens
type MintParams struct {
	Mint      solana.PublicKey `json:"mint"`
	To        solana.PublicKey `json:"to"`
	Amount    uint64           `json:"amount"`
	Authority solana.PublicKey `json:"authority"`
	Decimals  uint8            `json:"decimals"`
}

// TransactionParams represents transaction parameters
type TransactionParams struct {
	Instructions    []solana.Instruction `json:"instructions"`
	Payer          solana.PublicKey     `json:"payer"`
	RecentBlockhash solana.Hash         `json:"recent_blockhash"`
	FeePayer       solana.PublicKey     `json:"fee_payer"`
}