package types

import (
	"github.com/gagliardetto/solana-go"
)

// Block represents a Solana block
type Block struct {
	Blockhash         string              `json:"blockhash"`
	PreviousBlockhash string              `json:"previous_blockhash"`
	ParentSlot        uint64              `json:"parent_slot"`
	Slot              uint64              `json:"slot"`
	BlockTime         *int64              `json:"block_time"`
	BlockHeight       *uint64             `json:"block_height"`
	Transactions      []TransactionWithMeta `json:"transactions"`
	Rewards           []Reward            `json:"rewards"`
}

// TransactionWithMeta represents a transaction with metadata
type TransactionWithMeta struct {
	Transaction *solana.Transaction `json:"transaction"`
	Meta        *TransactionMeta    `json:"meta"`
	Version     *uint8              `json:"version"`
}

// TransactionMeta represents transaction metadata
type TransactionMeta struct {
	Fee               uint64                  `json:"fee"`
	PreBalances       []uint64                `json:"pre_balances"`
	PostBalances      []uint64                `json:"post_balances"`
	InnerInstructions []InnerInstructions     `json:"inner_instructions"`
	PreTokenBalances  []TokenBalance          `json:"pre_token_balances"`
	PostTokenBalances []TokenBalance          `json:"post_token_balances"`
	LogMessages       []string                `json:"log_messages"`
	Rewards           []Reward                `json:"rewards"`
	LoadedAddresses   *LoadedAddresses        `json:"loaded_addresses"`
	ComputeUnitsConsumed *uint64             `json:"compute_units_consumed"`
	Err               any                     `json:"err"`
}

// InnerInstructions represents inner instructions
type InnerInstructions struct {
	Index        uint8                      `json:"index"`
	Instructions []solana.CompiledInstruction `json:"instructions"`
}

// TokenBalance represents token balance information
type TokenBalance struct {
	AccountIndex  uint8               `json:"account_index"`
	Mint          string              `json:"mint"`
	Owner         *string             `json:"owner"`
	ProgramId     *string             `json:"program_id"`
	UITokenAmount UITokenAmount       `json:"ui_token_amount"`
}

// UITokenAmount represents UI-friendly token amount
type UITokenAmount struct {
	Amount         string  `json:"amount"`
	Decimals       uint8   `json:"decimals"`
	UIAmount       *float64 `json:"ui_amount"`
	UIAmountString string  `json:"ui_amount_string"`
}

// Reward represents a reward
type Reward struct {
	Pubkey      string  `json:"pubkey"`
	Lamports    int64   `json:"lamports"`
	PostBalance uint64  `json:"post_balance"`
	RewardType  *string `json:"reward_type"`
	Commission  *uint8  `json:"commission"`
}

// LoadedAddresses represents loaded addresses
type LoadedAddresses struct {
	Writable []string `json:"writable"`
	Readonly []string `json:"readonly"`
}

// BlockCommitment represents block commitment levels
type BlockCommitment string

const (
	CommitmentFinalized BlockCommitment = "finalized"
	CommitmentConfirmed BlockCommitment = "confirmed"
	CommitmentProcessed BlockCommitment = "processed"
)