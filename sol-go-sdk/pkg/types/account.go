package types

import (
	"github.com/gagliardetto/solana-go"
)

// Account represents a Solana account
type Account struct {
	PublicKey solana.PublicKey `json:"public_key"`
	Lamports  uint64           `json:"lamports"`
	Data      []byte           `json:"data"`
	Owner     solana.PublicKey `json:"owner"`
	Executable bool            `json:"executable"`
	RentEpoch  uint64          `json:"rent_epoch"`
}

// AccountInfo represents account information returned by RPC
type AccountInfo struct {
	Account    *Account `json:"account"`
	Slot       uint64   `json:"slot"`
	Executable bool     `json:"executable"`
}

// TokenAccount represents a token account
type TokenAccount struct {
	Mint                     solana.PublicKey `json:"mint"`
	Owner                    solana.PublicKey `json:"owner"`
	Amount                   uint64           `json:"amount"`
	Delegate                 *solana.PublicKey `json:"delegate,omitempty"`
	State                    TokenAccountState `json:"state"`
	IsNative                 *uint64          `json:"is_native,omitempty"`
	DelegatedAmount          uint64           `json:"delegated_amount"`
	CloseAuthority           *solana.PublicKey `json:"close_authority,omitempty"`
}

// TokenAccountState represents the state of a token account
type TokenAccountState uint8

const (
	TokenAccountStateUninitialized TokenAccountState = iota
	TokenAccountStateInitialized
	TokenAccountStateFrozen
)