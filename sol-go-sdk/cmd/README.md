# Solana Go SDK 演示程序

本目录包含了各种演示程序，展示如何使用 Solana Go SDK 的不同功能。

## 演示程序列表

### 1. 钱包演示 (`wallet-demo`)
演示钱包的创建、管理和基本操作。

**功能包括:**
- 创建新钱包
- 从助记词恢复钱包
- 多账户派生
- 地址验证
- 获取账户信息和余额

**运行命令:**
```bash
go run ./cmd/wallet-demo/main.go
```

### 2. RPC 演示 (`rpc-demo`)
演示 RPC 客户端的各种功能。

**功能包括:**
- 节点健康检查
- 获取版本信息
- 获取区块哈希和插槽信息
- 获取账户信息
- 获取纪元信息
- 计算租金豁免金额

**运行命令:**
```bash
go run ./cmd/rpc-demo/main.go
```

### 3. WebSocket 演示 (`websocket-demo`)
演示实时数据流功能。

**功能包括:**
- 订阅插槽更新
- 实时接收区块链数据

**运行命令:**
```bash
go run ./cmd/websocket-demo/main.go
```

### 4. 交易构建演示 (`transaction-demo`)
演示交易构建和处理功能。

**功能包括:**
- 基本转账交易构建
- 交易签名
- 交易模拟
- 复杂交易构建（多指令）
- 交易验证

**运行命令:**
```bash
go run ./cmd/transaction-demo/main.go
```

## 使用说明

### 前置条件
1. 确保已安装 Go 1.19 或更高版本
2. 确保网络连接正常，能够访问 Solana 开发网络

### 运行演示
1. 进入项目根目录
2. 运行任意演示程序：
   ```bash
   go run ./cmd/[演示名称]/main.go
   ```

### 网络设置
所有演示程序默认连接到 Solana 开发网络 (devnet)。如果需要连接到其他网络，可以修改代码中的 RPC 端点：

- **主网**: `https://api.mainnet-beta.solana.com`
- **测试网**: `https://api.testnet.solana.com`
- **开发网**: `https://api.devnet.solana.com`

## 演示程序特点

### 安全性
- 所有演示程序都使用测试网络
- 使用预设的测试助记词，不会影响真实资产
- 交易只进行模拟，不会实际发送到网络

### 教育性
- 每个演示都包含详细的中文注释
- 展示了错误处理的最佳实践
- 包含了参数验证示例

### 实用性
- 代码可以直接复制到实际项目中使用
- 展示了完整的操作流程
- 提供了性能和大小估算

## 故障排除

### 常见问题

1. **网络连接问题**
   - 确保能够访问 Solana RPC 端点
   - 检查防火墙设置
   - 尝试更换网络

2. **编译错误**
   - 确保 Go 版本正确
   - 运行 `go mod tidy` 更新依赖
   - 检查 GOPATH 和 GOROOT 设置

3. **运行时错误**
   - 检查网络连接
   - 确认 RPC 端点可用
   - 查看控制台输出的错误信息

### 调试建议
- 使用 `-v` 参数查看详细输出
- 检查网络连接状态
- 确认 Solana 网络状态正常

## 扩展和自定义

### 添加新的演示
1. 创建新的目录 `cmd/your-demo`
2. 创建 `main.go` 文件
3. 实现你的演示逻辑
4. 更新这个 README 文件

### 自定义配置
- 修改 RPC 端点
- 调整超时设置
- 添加自定义参数验证

## 许可证
本演示程序与主项目使用相同的 MIT 许可证。