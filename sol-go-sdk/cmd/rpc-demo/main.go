package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"sol-go-sdk/internal/config"
	"sol-go-sdk/pkg/rpc"

	"github.com/gagliardetto/solana-go"
	solrpc "github.com/gagliardetto/solana-go/rpc"
)

func main() {
	fmt.Println("Solana Go SDK - RPC 演示")
	fmt.Println("=======================")

	// 创建 RPC 客户端
	fmt.Println("\n1. 创建 RPC 客户端...")
	client := rpc.NewClient(config.MainnetRPCEndpoint)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 检查节点健康状况
	fmt.Println("\n2. 检查节点健康状况...")
	_, err := client.GetHealth(ctx)
	if err != nil {
		log.Printf("节点健康检查失败: %v", err)
	} else {
		fmt.Println("✅ 节点健康状况良好")
	}

	// 获取版本信息
	fmt.Println("\n3. 获取版本信息...")
	version, err := client.GetVersion(ctx)
	if err != nil {
		log.Printf("获取版本信息失败: %v", err)
	} else {
		fmt.Printf("Solana 版本: %s\n", version.SolanaCore)
		fmt.Printf("功能集: %d\n", version.FeatureSet)
	}

	// 获取最新区块哈希
	fmt.Println("\n4. 获取最新区块哈希...")
	blockhash, err := client.GetLatestBlockhash(ctx, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取最新区块哈希失败: %v", err)
	} else {
		fmt.Printf("最新区块哈希: %s\n", blockhash.Value.Blockhash)
		fmt.Printf("最后有效区块高度: %d\n", blockhash.Value.LastValidBlockHeight)
	}

	// 获取当前插槽
	fmt.Println("\n5. 获取当前插槽...")
	slot, err := client.GetSlot(ctx, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取插槽失败: %v", err)
	} else {
		fmt.Printf("当前插槽: %d\n", slot)
	}

	// 获取纪元信息
	fmt.Println("\n6. 获取纪元信息...")
	epochInfo, err := client.GetEpochInfo(ctx, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取纪元信息失败: %v", err)
	} else {
		fmt.Printf("当前纪元: %d\n", epochInfo.Epoch)
		fmt.Printf("插槽索引: %d\n", epochInfo.SlotIndex)
		fmt.Printf("纪元中的插槽数: %d\n", epochInfo.SlotsInEpoch)
	}

	// 获取系统程序账户余额
	fmt.Println("\n7. 获取系统程序账户余额...")
	systemProgramID := solana.MustPublicKeyFromBase58("11111111111111111111111111111111")
	balance, err := client.GetBalance(ctx, systemProgramID, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取系统程序余额失败: %v", err)
	} else {
		fmt.Printf("系统程序余额: %d lamports\n", balance.Value)
	}

	// 获取账户信息
	fmt.Println("\n8. 获取系统程序账户信息...")
	accountInfo, err := client.GetAccountInfo(ctx, systemProgramID)
	if err != nil {
		log.Printf("获取账户信息失败: %v", err)
	} else {
		if accountInfo.Value != nil {
			fmt.Printf("账户所有者: %s\n", accountInfo.Value.Owner)
			fmt.Printf("账户余额: %d lamports\n", accountInfo.Value.Lamports)
			fmt.Printf("可执行: %v\n", accountInfo.Value.Executable)
			fmt.Printf("租金纪元: %d\n", accountInfo.Value.RentEpoch)
		}
	}

	// 获取最小租金豁免余额
	fmt.Println("\n9. 获取最小租金豁免余额...")
	minRent, err := client.GetMinimumBalanceForRentExemption(ctx, 0, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取最小租金豁免余额失败: %v", err)
	} else {
		fmt.Printf("0 字节账户的最小租金豁免余额: %d lamports\n", minRent)
	}

	// 获取代币账户的租金豁免余额
	tokenAccountRent, err := client.GetMinimumBalanceForRentExemption(ctx, 165, solrpc.CommitmentFinalized)
	if err != nil {
		log.Printf("获取代币账户租金豁免余额失败: %v", err)
	} else {
		fmt.Printf("代币账户的最小租金豁免余额: %d lamports\n", tokenAccountRent)
	}

	fmt.Println("\n✅ RPC 演示完成!")
}
