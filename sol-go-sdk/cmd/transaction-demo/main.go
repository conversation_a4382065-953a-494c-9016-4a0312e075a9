package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"sol-go-sdk/pkg/rpc"
	"sol-go-sdk/pkg/types"
	"sol-go-sdk/pkg/wallet"
	"sol-go-sdk/pkg/transaction"
)

func main() {
	fmt.Println("Solana Go SDK - 交易构建演示")
	fmt.Println("============================")

	// 创建钱包
	fmt.Println("\n1. 创建钱包...")
	sender, err := wallet.NewWalletFromMnemonic(
		"abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about",
		"",
	)
	if err != nil {
		log.Fatalf("创建发送方钱包失败: %v", err)
	}
	fmt.Printf("发送方地址: %s\n", sender.Address())

	recipient, err := wallet.NewWallet()
	if err != nil {
		log.Fatalf("创建接收方钱包失败: %v", err)
	}
	fmt.Printf("接收方地址: %s\n", recipient.Address())

	// 创建 RPC 客户端
	fmt.Println("\n2. 创建 RPC 客户端...")
	client := rpc.NewClient("https://api.devnet.solana.com")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取最新区块哈希
	fmt.Println("\n3. 获取最新区块哈希...")
	blockhash, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
	if err != nil {
		log.Fatalf("获取最新区块哈希失败: %v", err)
	}
	fmt.Printf("最新区块哈希: %s\n", blockhash.Value.Blockhash)

	// 演示 1: 使用交易构建器构建基本转账
	fmt.Println("\n4. 使用交易构建器构建基本转账...")
	builder := transaction.NewBuilder()
	
	// 创建转账指令
	transferInstruction := system.NewTransferInstruction(
		1000000, // 0.001 SOL
		sender.PublicKey(),
		recipient.PublicKey(),
	).Build()

	// 构建交易
	tx, err := builder.
		SetPayer(sender.PublicKey()).
		SetRecentBlockhash(blockhash.Value.Blockhash).
		AddInstruction(transferInstruction).
		Build()
	if err != nil {
		log.Printf("构建交易失败: %v", err)
	} else {
		fmt.Printf("✅ 交易构建成功，包含 %d 个指令\n", len(tx.Message.Instructions))
		
		// 估算交易大小
		size, err := builder.EstimateTransactionSize()
		if err != nil {
			log.Printf("估算交易大小失败: %v", err)
		} else {
			fmt.Printf("估算交易大小: %d 字节\n", size)
		}
	}

	// 演示 2: 签名交易
	fmt.Println("\n5. 签名交易...")
	if tx != nil {
		signedTx, err := sender.SignTransaction(tx)
		if err != nil {
			log.Printf("签名交易失败: %v", err)
		} else {
			fmt.Printf("✅ 交易签名成功\n")
			fmt.Printf("交易签名: %s\n", signedTx.Signatures[0])
			
			// 模拟交易
			fmt.Println("\n6. 模拟交易...")
			simulation, err := client.SimulateTransaction(ctx, signedTx, types.CommitmentFinalized)
			if err != nil {
				log.Printf("模拟交易失败: %v", err)
			} else {
				if simulation.Value.Err != nil {
					fmt.Printf("❌ 交易模拟失败: %v\n", simulation.Value.Err)
				} else {
					fmt.Printf("✅ 交易模拟成功\n")
					fmt.Printf("消耗的计算单元: %d\n", simulation.Value.UnitsConsumed)
					if len(simulation.Value.Logs) > 0 {
						fmt.Printf("日志: %v\n", simulation.Value.Logs[0])
					}
				}
			}
		}
	}

	// 演示 3: 构建复杂交易（多个指令）
	fmt.Println("\n7. 构建复杂交易（多个指令）...")
	complexBuilder := transaction.NewBuilder()
	
	// 创建多个转账指令
	instruction1 := system.NewTransferInstruction(
		500000, // 0.0005 SOL
		sender.PublicKey(),
		recipient.PublicKey(),
	).Build()
	
	instruction2 := system.NewTransferInstruction(
		300000, // 0.0003 SOL
		sender.PublicKey(),
		recipient.PublicKey(),
	).Build()

	complexTx, err := complexBuilder.
		SetPayer(sender.PublicKey()).
		SetRecentBlockhash(blockhash.Value.Blockhash).
		AddInstruction(instruction1).
		AddInstruction(instruction2).
		Build()
	if err != nil {
		log.Printf("构建复杂交易失败: %v", err)
	} else {
		fmt.Printf("✅ 复杂交易构建成功，包含 %d 个指令\n", len(complexTx.Message.Instructions))
		
		// 验证交易
		if err := complexBuilder.ValidateTransaction(); err != nil {
			log.Printf("交易验证失败: %v", err)
		} else {
			fmt.Printf("✅ 交易验证通过\n")
		}
	}

	// 演示 4: 验证转账参数
	fmt.Println("\n8. 验证转账参数...")
	transferParams := types.TransferParams{
		From:   sender.PublicKey(),
		To:     recipient.PublicKey(),
		Amount: 1000000,
	}
	
	// 由于 transfer.go 有问题，我们手动验证
	fmt.Printf("验证转账参数:\n")
	fmt.Printf("  从地址: %s\n", transferParams.From)
	fmt.Printf("  到地址: %s\n", transferParams.To)
	fmt.Printf("  金额: %d lamports\n", transferParams.Amount)
	
	// 基本验证逻辑
	if transferParams.From.IsZero() {
		fmt.Printf("❌ 发送方地址不能为空\n")
	} else if transferParams.To.IsZero() {
		fmt.Printf("❌ 接收方地址不能为空\n")
	} else if transferParams.Amount == 0 {
		fmt.Printf("❌ 金额必须大于0\n")
	} else if transferParams.From.Equals(transferParams.To) {
		fmt.Printf("❌ 发送方和接收方不能相同\n")
	} else {
		fmt.Printf("✅ 转账参数验证通过\n")
	}

	// 演示 5: 创建账户指令
	fmt.Println("\n9. 创建账户指令演示...")
	newAccount, err := wallet.NewWallet()
	if err != nil {
		log.Printf("创建新账户失败: %v", err)
	} else {
		fmt.Printf("新账户地址: %s\n", newAccount.Address())
		
		// 获取创建账户所需的最小余额
		minBalance, err := client.GetMinimumBalanceForRentExemption(ctx, 0, types.CommitmentFinalized)
		if err != nil {
			log.Printf("获取最小余额失败: %v", err)
		} else {
			fmt.Printf("创建账户所需最小余额: %d lamports\n", minBalance)
			
			// 创建账户指令
			createAccountInstruction := system.NewCreateAccountInstruction(
				minBalance,
				0,
				solana.SystemProgramID,
				sender.PublicKey(),
				newAccount.PublicKey(),
			).Build()
			
			// 构建包含创建账户指令的交易
			_, err := transaction.NewBuilder().
				SetPayer(sender.PublicKey()).
				SetRecentBlockhash(blockhash.Value.Blockhash).
				AddInstruction(createAccountInstruction).
				Build()
			if err != nil {
				log.Printf("构建创建账户交易失败: %v", err)
			} else {
				fmt.Printf("✅ 创建账户交易构建成功\n")
			}
		}
	}

	fmt.Println("\n✅ 交易构建演示完成!")
}