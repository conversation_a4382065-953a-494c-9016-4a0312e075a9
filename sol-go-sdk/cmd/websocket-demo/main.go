package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"sol-go-sdk/pkg/rpc"
)

func main() {
	fmt.Println("Solana Go SDK - WebSocket 演示")
	fmt.Println("=============================")

	// 创建 WebSocket 客户端
	fmt.Println("\n1. 创建 WebSocket 客户端...")
	wsClient, err := rpc.NewSimpleWebSocketClient("wss://api.devnet.solana.com")
	if err != nil {
		log.Fatalf("创建 WebSocket 客户端失败: %v", err)
	}
	defer wsClient.Close()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 订阅插槽更新
	fmt.Println("\n2. 订阅插槽更新...")
	slotChan, err := wsClient.SubscribeToSlots(ctx)
	if err != nil {
		log.Fatalf("订阅插槽失败: %v", err)
	}

	// 处理插槽更新
	fmt.Println("\n3. 等待插槽更新...")
	slotCount := 0
	for slot := range slotChan {
		slotCount++
		fmt.Printf("📦 插槽更新 #%d: 插槽 %d, 父插槽: %d\n", slotCount, slot.Slot, slot.Parent)
		
		// 只显示前 5 个插槽更新
		if slotCount >= 5 {
			break
		}
	}

	fmt.Println("\n✅ WebSocket 演示完成!")
}