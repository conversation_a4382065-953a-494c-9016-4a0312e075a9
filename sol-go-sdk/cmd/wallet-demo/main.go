package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gagliardetto/solana-go"
	"sol-go-sdk/pkg/rpc"
	"sol-go-sdk/pkg/types"
	"sol-go-sdk/pkg/wallet"
)

func main() {
	fmt.Println("Solana Go SDK - 钱包演示")
	fmt.Println("=======================")

	// 示例 1: 创建新钱包
	fmt.Println("\n1. 创建新钱包...")
	newWallet, err := wallet.NewWallet()
	if err != nil {
		log.Fatalf("创建钱包失败: %v", err)
	}
	fmt.Printf("新钱包地址: %s\n", newWallet.Address())
	fmt.Printf("助记词: %s\n", newWallet.Mnemonic())

	// 示例 2: 从助记词创建钱包
	fmt.Println("\n2. 从助记词创建钱包...")
	mnemonic := "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
	walletFromMnemonic, err := wallet.NewWalletFromMnemonic(mnemonic, "")
	if err != nil {
		log.Fatalf("从助记词创建钱包失败: %v", err)
	}
	fmt.Printf("从助记词创建的钱包地址: %s\n", walletFromMnemonic.Address())

	// 示例 3: 创建 RPC 客户端
	fmt.Println("\n3. 创建 RPC 客户端...")
	client := rpc.NewClient("https://api.devnet.solana.com")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 示例 4: 获取最新区块哈希
	fmt.Println("\n4. 获取最新区块哈希...")
	blockhashResult, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
	if err != nil {
		log.Printf("获取最新区块哈希失败: %v", err)
	} else {
		fmt.Printf("最新区块哈希: %s\n", blockhashResult.Value.Blockhash)
	}

	// 示例 5: 获取账户余额
	fmt.Println("\n5. 获取账户余额...")
	balance, err := client.GetBalance(ctx, walletFromMnemonic.PublicKey(), types.CommitmentFinalized)
	if err != nil {
		log.Printf("获取余额失败: %v", err)
	} else {
		fmt.Printf("账户余额: %d lamports\n", balance.Value)
	}

	// 示例 6: 获取账户信息
	fmt.Println("\n6. 获取账户信息...")
	accountInfo, err := client.GetAccountInfo(ctx, walletFromMnemonic.PublicKey())
	if err != nil {
		log.Printf("获取账户信息失败: %v", err)
	} else {
		if accountInfo.Value == nil {
			fmt.Println("账户不存在或未激活")
		} else {
			fmt.Printf("账户所有者: %s\n", accountInfo.Value.Owner)
			fmt.Printf("账户余额: %d lamports\n", accountInfo.Value.Lamports)
			fmt.Printf("账户数据大小: %d bytes\n", len(accountInfo.Value.Data.GetBinary()))
		}
	}

	// 示例 7: 多账户派生
	fmt.Println("\n7. 派生多个账户...")
	accounts, err := walletFromMnemonic.DeriveMultipleAccounts(3)
	if err != nil {
		log.Printf("派生多个账户失败: %v", err)
	} else {
		fmt.Printf("派生了 %d 个账户:\n", len(accounts))
		for i, account := range accounts {
			fmt.Printf("  账户 %d: %s\n", i, account.Address())
		}
	}

	// 示例 8: 验证地址
	fmt.Println("\n8. 验证地址...")
	validAddress := "********************************"
	invalidAddress := "invalid_address"
	
	if _, err := solana.PublicKeyFromBase58(validAddress); err != nil {
		fmt.Printf("地址 %s 无效: %v\n", validAddress, err)
	} else {
		fmt.Printf("地址 %s 有效\n", validAddress)
	}
	
	if _, err := solana.PublicKeyFromBase58(invalidAddress); err != nil {
		fmt.Printf("地址 %s 无效: %v\n", invalidAddress, err)
	} else {
		fmt.Printf("地址 %s 有效\n", invalidAddress)
	}

	// 示例 9: 获取版本信息
	fmt.Println("\n9. 获取 RPC 节点版本...")
	version, err := client.GetVersion(ctx)
	if err != nil {
		log.Printf("获取版本信息失败: %v", err)
	} else {
		fmt.Printf("Solana 版本: %s\n", version.SolanaCore)
	}

	// 示例 10: 获取当前插槽
	fmt.Println("\n10. 获取当前插槽...")
	slot, err := client.GetSlot(ctx, types.CommitmentFinalized)
	if err != nil {
		log.Printf("获取插槽失败: %v", err)
	} else {
		fmt.Printf("当前插槽: %d\n", slot)
	}

	fmt.Println("\n✅ 钱包演示完成!")
}