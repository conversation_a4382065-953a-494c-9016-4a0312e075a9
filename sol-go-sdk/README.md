# Solana Go SDK

一个功能完整的 Solana 区块链 Go SDK，基于优秀的 [gagliardetto/solana-go](https://github.com/gagliardetto/solana-go) 库构建。

## 特性

- 🔐 **钱包管理**: 基于 BIP32 的助记词钱包创建
- 🌐 **RPC 客户端**: 功能完整的 RPC 客户端，支持超时配置
- 📡 **WebSocket 支持**: 实时区块链数据流
- 💰 **交易构建**: 支持 SOL 转账、Token Program 和 Token 2022 Program
- 🔧 **工具函数**: 编码、验证和辅助函数
- 📊 **类型安全**: 所有操作的完整类型定义

## 安装

```bash
go get github.com/your-username/sol-go-sdk
```

## 快速开始

### 创建钱包

```go
package main

import (
    "fmt"
    "log"
    
    "sol-go-sdk/pkg/wallet"
)

func main() {
    // 创建带有随机助记词的新钱包
    w, err := wallet.NewWallet()
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("地址: %s\n", w.Address())
    fmt.Printf("助记词: %s\n", w.Mnemonic())
    
    // 从现有助记词创建钱包
    mnemonic := "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
    w2, err := wallet.NewWalletFromMnemonic(mnemonic, "")
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("从助记词创建的地址: %s\n", w2.Address())
}
```

### RPC 客户端使用

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "sol-go-sdk/pkg/rpc"
    "sol-go-sdk/pkg/types"
    "sol-go-sdk/pkg/wallet"
)

func main() {
    // 创建 RPC 客户端
    client := rpc.NewClient("https://api.devnet.solana.com")
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // 获取最新区块哈希
    result, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("最新区块哈希: %s\n", result.Value.Blockhash)
    
    // 获取账户余额
    wallet, _ := wallet.NewWalletFromMnemonic("abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about", "")
    balance, err := client.GetBalance(ctx, wallet.PublicKey(), types.CommitmentFinalized)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("余额: %d lamports\n", balance.Value)
}
```

### 构建交易

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/gagliardetto/solana-go"
    "sol-go-sdk/pkg/rpc"
    "sol-go-sdk/pkg/types"
    "sol-go-sdk/pkg/wallet"
    "sol-go-sdk/pkg/transaction"
)

func main() {
    // 创建钱包
    sender, _ := wallet.NewWalletFromMnemonic("abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about", "")
    recipient, _ := wallet.NewWallet()
    
    // 创建 RPC 客户端
    client := rpc.NewClient("https://api.devnet.solana.com")
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // 获取最新区块哈希
    blockhash, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
    if err != nil {
        log.Fatal(err)
    }
    
    // 构建 SOL 转账交易
    tx, err := transaction.BuildTransfer(
        sender.PublicKey(),
        recipient.PublicKey(),
        1000000, // 0.001 SOL
        blockhash.Value.Blockhash,
    )
    if err != nil {
        log.Fatal(err)
    }
    
    // 签名交易
    signedTx, err := sender.SignTransaction(tx)
    if err != nil {
        log.Fatal(err)
    }
    
    // 发送交易
    signature, err := client.SendTransaction(ctx, signedTx)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("交易已发送: %s\n", signature)
}
```

### 代币操作

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/gagliardetto/solana-go"
    "sol-go-sdk/pkg/rpc"
    "sol-go-sdk/pkg/types"
    "sol-go-sdk/pkg/wallet"
    "sol-go-sdk/pkg/transaction"
)

func main() {
    // 创建钱包
    sender, _ := wallet.NewWalletFromMnemonic("abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about", "")
    recipient, _ := wallet.NewWallet()
    
    // 代币铸造地址 (USDC)
    usdcMint := solana.MustPublicKeyFromBase58("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")
    
    // 获取关联代币账户
    senderTokenAccount, _ := transaction.GetAssociatedTokenAccount(sender.PublicKey(), usdcMint)
    recipientTokenAccount, _ := transaction.GetAssociatedTokenAccount(recipient.PublicKey(), usdcMint)
    
    // 创建 RPC 客户端
    client := rpc.NewClient("https://api.devnet.solana.com")
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // 获取最新区块哈希
    blockhash, err := client.GetLatestBlockhash(ctx, types.CommitmentFinalized)
    if err != nil {
        log.Fatal(err)
    }
    
    // 构建代币转账交易
    tokenParams := types.TokenTransferParams{
        From:     senderTokenAccount,
        To:       recipientTokenAccount,
        Amount:   1000000, // 1 USDC
        Mint:     usdcMint,
        Owner:    sender.PublicKey(),
        Decimals: 6,
    }
    
    tx, err := transaction.BuildTokenTransferWithParams(tokenParams, blockhash.Value.Blockhash)
    if err != nil {
        log.Fatal(err)
    }
    
    // 签名并发送交易
    signedTx, err := sender.SignTransaction(tx)
    if err != nil {
        log.Fatal(err)
    }
    
    signature, err := client.SendTransaction(ctx, signedTx)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("代币转账已发送: %s\n", signature)
}
```

### WebSocket 实时数据流

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "sol-go-sdk/pkg/rpc"
    "sol-go-sdk/pkg/types"
)

func main() {
    // 创建 WebSocket 客户端
    wsClient, err := rpc.NewWebSocketClient("wss://api.devnet.solana.com")
    if err != nil {
        log.Fatal(err)
    }
    defer wsClient.Close()
    
    ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
    defer cancel()
    
    // 订阅插槽更新
    slotChan, err := wsClient.SubscribeToSlots(ctx)
    if err != nil {
        log.Fatal(err)
    }
    
    // 处理插槽更新
    for slot := range slotChan {
        fmt.Printf("新插槽: %d, 父插槽: %d\n", slot.Slot, slot.Parent)
    }
}
```

## 架构

SDK 按以下模块组织：

### 核心模块

- **`pkg/wallet`**: 钱包创建和管理，支持 BIP32 派生
- **`pkg/rpc`**: 区块链交互的 RPC 客户端
- **`pkg/transaction`**: 各种操作的交易构建
- **`pkg/types`**: 类型定义和数据结构
- **`pkg/utils`**: 编码和验证的工具函数

### 内部模块

- **`internal/config`**: 配置常量和默认值

### 示例

- **`cmd/examples`**: 演示 SDK 用法的示例应用
- **`pkg/examples`**: 可复用的示例代码

## 支持的操作

### 钱包操作
- 生成助记词（12-24 个词）
- 从助记词创建钱包
- 自定义路径的 BIP32 密钥派生
- 多账户派生
- 交易签名

### RPC 操作
- 获取账户信息和余额
- 获取最新区块哈希和区块数据
- 发送和模拟交易
- 获取交易历史
- 监控交易状态

### 交易构建
- SOL 转账
- Token Program 操作（转账、铸造、销毁等）
- Token 2022 Program 操作
- 关联代币账户管理
- 复杂的多指令交易

### WebSocket 实时数据流
- 实时插槽更新
- 区块通知
- 账户变更监控
- 交易确认追踪
- 程序账户更新

## 配置

SDK 使用合理的默认值，但可以自定义：

```go
// 自定义超时的 RPC 客户端
client := rpc.NewClientWithOptions(rpc.ClientOptions{
    Endpoint: "https://api.mainnet-beta.solana.com",
    Timeout:  45 * time.Second,
})

// 自定义 WebSocket 客户端
wsClient, err := rpc.NewWebSocketClientWithOptions(rpc.WebSocketClientOptions{
    Endpoint: "wss://api.mainnet-beta.solana.com",
    Timeout:  120 * time.Second,
})

// 自定义钱包派生
wallet, err := wallet.NewWalletWithOptions(wallet.WalletOptions{
    Mnemonic:       "你的助记词",
    Passphrase:     "可选密码",
    DerivationPath: "m/44'/501'/0'/0'",
    AccountIndex:   0,
})
```

## 错误处理

SDK 提供完善的错误处理：

```go
// 处理前验证输入
if err := transaction.ValidateTransfer(transferParams); err != nil {
    log.Printf("无效的转账参数: %v", err)
    return
}

// 处理 RPC 错误
balance, err := client.GetBalance(ctx, pubkey, commitment)
if err != nil {
    log.Printf("获取余额失败: %v", err)
    return
}

// 处理交易错误
signature, err := client.SendTransaction(ctx, transaction)
if err != nil {
    log.Printf("发送交易失败: %v", err)
    return
}
```

## 测试

运行测试套件：

```bash
go test ./...
```

运行特定测试：

```bash
go test ./pkg/wallet/...
go test ./pkg/rpc/...
go test ./pkg/transaction/...
```

## 示例

查看 `cmd/` 目录中的演示程序：

- `wallet-demo`: 钱包创建和管理演示
- `rpc-demo`: RPC 客户端功能演示
- `websocket-demo`: WebSocket 实时数据流演示
- `transaction-demo`: 交易构建和处理演示

运行示例：

```bash
# 钱包演示
go run ./cmd/wallet-demo/main.go

# RPC 演示
go run ./cmd/rpc-demo/main.go

# WebSocket 演示
go run ./cmd/websocket-demo/main.go

# 交易构建演示
go run ./cmd/transaction-demo/main.go
```

详细使用说明请参考 [cmd/README.md](cmd/README.md)。

## 贡献

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m '添加一些令人惊叹的特性'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 致谢

- 基于 [gagliardetto/solana-go](https://github.com/gagliardetto/solana-go) 构建
- 使用 [cosmos/go-bip39](https://github.com/cosmos/go-bip39) 生成助记词
- 受 Solana 生态系统启发

## 支持

如需帮助和支持：

- 在 GitHub 上提交 issue
- 查看示例目录
- 阅读 Go 文档

## 网络配置

### 主网
```go
client := rpc.NewClient("https://api.mainnet-beta.solana.com")
```

### 测试网
```go
client := rpc.NewClient("https://api.testnet.solana.com")
```

### 开发网
```go
client := rpc.NewClient("https://api.devnet.solana.com")
```

## 常见用例

### 1. 批量创建钱包
```go
wallets := make([]*wallet.Wallet, 10)
for i := 0; i < 10; i++ {
    w, err := wallet.NewWallet()
    if err != nil {
        log.Fatal(err)
    }
    wallets[i] = w
}
```

### 2. 监控账户余额变化
```go
wsClient, _ := rpc.NewWebSocketClient("wss://api.devnet.solana.com")
accountChan, _ := wsClient.SubscribeToAccountUpdates(ctx, publicKey, types.CommitmentFinalized)

for update := range accountChan {
    fmt.Printf("余额变化: %d lamports\n", update.Account.Lamports)
}
```

### 3. 批量转账
```go
builder := transaction.NewTransferBuilder()
for _, recipient := range recipients {
    builder.Transfer(sender.PublicKey(), recipient, amount)
}
tx, _ := builder.SetPayer(sender.PublicKey()).SetRecentBlockhash(blockhash).Build()
```

---

**注意**: 此 SDK 用于教育和开发目的。在生产环境中使用前请充分测试。