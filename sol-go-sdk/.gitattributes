# Auto detect text files and perform LF normalization
* text=auto

# Go files
*.go text eol=lf

# Markdown files
*.md text eol=lf

# YAML files
*.yml text eol=lf
*.yaml text eol=lf

# JSON files
*.json text eol=lf

# Configuration files
*.toml text eol=lf
*.ini text eol=lf
*.cfg text eol=lf

# Shell scripts
*.sh text eol=lf

# Make files
Makefile text eol=lf
*.mk text eol=lf

# License and readme files
LICENSE text eol=lf
README text eol=lf
CHANGELOG text eol=lf
CONTRIBUTING text eol=lf

# Go module files
go.mod text eol=lf
go.sum text eol=lf

# Binary files
*.exe binary
*.dll binary
*.so binary
*.dylib binary

# Image files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary

# Archive files
*.zip binary
*.tar binary
*.tar.gz binary
*.tar.bz2 binary
*.tar.xz binary
*.rar binary
*.7z binary

# Document files
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary